package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	// Initialize global config
	global.GVA_CONFIG = &global.Config{}

	// Initialize database connection
	if err := global.InitializeDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	ctx := context.Background()
	service := activity_cashback.NewActivityCashbackService()

	// Parse the task ID from your created task
	taskID, err := uuid.Parse("e1871fd9-3b47-4555-bdce-99bbfad4a38c")
	if err != nil {
		log.Fatalf("Invalid task ID: %v", err)
	}

	fmt.Println("=== Debug MEME Trade Task Issue ===")

	// 1. Check if the task exists and its details
	fmt.Println("\n1. Checking task details...")
	task, err := service.GetTaskByID(ctx, taskID)
	if err != nil {
		fmt.Printf("Error getting task: %v\n", err)
		return
	}

	fmt.Printf("Task found:\n")
	fmt.Printf("  - ID: %s\n", task.ID.String())
	fmt.Printf("  - Name: %s\n", task.Name)
	fmt.Printf("  - Category ID: %s\n", task.CategoryID)
	fmt.Printf("  - Task Type: %s\n", task.TaskType)
	fmt.Printf("  - Frequency: %s\n", task.Frequency)
	fmt.Printf("  - Points: %d\n", task.Points)
	fmt.Printf("  - Is Active: %t\n", task.IsActive)
	if task.TaskIdentifier != nil {
		fmt.Printf("  - Task Identifier: %s\n", *task.TaskIdentifier)
	} else {
		fmt.Printf("  - Task Identifier: nil\n")
	}

	// 2. Check category details
	fmt.Println("\n2. Checking category details...")
	// Get category repository directly since service doesn't expose GetCategoryByID
	categoryRepo := activity_cashback.NewTaskCategoryRepository()
	category, err := categoryRepo.GetByID(ctx, task.CategoryID)
	if err != nil {
		fmt.Printf("Error getting category: %v\n", err)
	} else {
		fmt.Printf("Category found:\n")
		fmt.Printf("  - ID: %s\n", category.ID)
		fmt.Printf("  - Name: %s\n", category.Name)
		fmt.Printf("  - Display Name: %s\n", category.DisplayName)
		fmt.Printf("  - Is Active: %t\n", category.IsActive)
	}

	// 3. Check if task can be found by category
	fmt.Println("\n3. Checking tasks in DAILY category...")
	dailyTasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		fmt.Printf("Error getting daily tasks: %v\n", err)
	} else {
		fmt.Printf("Found %d daily tasks:\n", len(dailyTasks))
		found := false
		for _, t := range dailyTasks {
			if t.ID == taskID {
				found = true
				fmt.Printf("  ✓ Target task found in daily category\n")
			}
			if t.TaskIdentifier != nil && *t.TaskIdentifier == model.TaskIDMemeTradeDaily {
				fmt.Printf("  - MEME_TRADE_DAILY task: %s (ID: %s)\n", t.Name, t.ID.String())
			}
		}
		if !found {
			fmt.Printf("  ✗ Target task NOT found in daily category\n")
		}
	}

	// 4. Check if task can be found by trading category
	fmt.Println("\n4. Checking tasks in TRADING category...")
	tradingTasks, err := service.GetTasksByCategory(ctx, model.CategoryTrading)
	if err != nil {
		fmt.Printf("Error getting trading tasks: %v\n", err)
	} else {
		fmt.Printf("Found %d trading tasks:\n", len(tradingTasks))
		found := false
		for _, t := range tradingTasks {
			if t.ID == taskID {
				found = true
				fmt.Printf("  ✓ Target task found in trading category\n")
			}
			if t.TaskIdentifier != nil && *t.TaskIdentifier == model.TaskIDMemeTradeDaily {
				fmt.Printf("  - MEME_TRADE_DAILY task: %s (ID: %s)\n", t.Name, t.ID.String())
			}
		}
		if !found {
			fmt.Printf("  ✗ Target task NOT found in trading category\n")
		}
	}

	// 5. Test task processing with a sample user
	fmt.Println("\n5. Testing task processing...")

	// Create a test user ID (you should replace this with your actual user ID)
	testUserID := uuid.New()
	fmt.Printf("Using test user ID: %s\n", testUserID.String())

	// Initialize user for activity cashback
	if err := service.InitializeUserForActivityCashback(ctx, testUserID); err != nil {
		fmt.Printf("Error initializing user: %v\n", err)
		return
	}

	// Prepare trade data
	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     100.0,
		"order_id":   uuid.New().String(),
		"user_id":    testUserID.String(),
	}

	// Create task processor manager
	taskManager := activity_cashback.NewTaskProcessorManager(service)

	// Try processing in daily category
	fmt.Printf("Trying to process MEME trade task in DAILY category...\n")
	err = taskManager.ProcessTaskByIdentifier(ctx, testUserID, model.TaskIDMemeTradeDaily, model.CategoryDaily, tradeData)
	if err != nil {
		fmt.Printf("Error processing in daily category: %v\n", err)
	} else {
		fmt.Println("✓ Task processed successfully in daily category!")
	}

	// Try processing in trading category
	fmt.Printf("Trying to process MEME trade task in TRADING category...\n")
	err = taskManager.ProcessTaskByIdentifier(ctx, testUserID, model.TaskIDMemeTradeDaily, model.CategoryTrading, tradeData)
	if err != nil {
		fmt.Printf("Error processing in trading category: %v\n", err)
	} else {
		fmt.Println("✓ Task processed successfully in trading category!")
	}

	// 6. Check user points after processing
	fmt.Println("\n6. Checking user points...")
	tierInfo, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting tier info: %v\n", err)
	} else {
		fmt.Printf("User tier info:\n")
		fmt.Printf("  - Current Tier: %d\n", tierInfo.CurrentTier)
		fmt.Printf("  - Total Points: %d\n", tierInfo.TotalPoints)
	}

	fmt.Println("\n=== Debug Complete ===")
}

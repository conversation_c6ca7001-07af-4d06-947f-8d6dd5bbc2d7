-- Check task categories
SELECT id, name, display_name, is_active FROM task_categories;

-- Check existing MEME trade tasks
SELECT id, name, task_identifier, category_id, points, is_active, created_at 
FROM activity_tasks 
WHERE task_identifier = 'MEME_TRADE_DAILY' OR name ILIKE '%meme%';

-- Check if the specific task exists
SELECT id, name, task_identifier, category_id, points, is_active, created_at 
FROM activity_tasks 
WHERE id = 'e1871fd9-3b47-4555-bdce-99bbfad4a38c';

-- Check recent tasks
SELECT id, name, task_identifier, category_id, points, is_active, created_at 
FROM activity_tasks 
ORDER BY created_at DESC 
LIMIT 5;
